# Story 3.4: 筛查结果处理引擎

## Status

Approved

## Story

**As a** 模拟引擎，
**I want** 处理筛查结果并触发后续行动，
**so that** 模拟完整的筛查-诊断-治疗流程。

## Acceptance Criteria

1. 实现筛查结果判定逻辑（阳性/阴性/不确定）
2. 模拟假阳性和假阴性结果的处理
3. 实现阳性结果的后续诊断流程触发
4. 添加筛查结果对个体状态的影响建模
5. 创建筛查结果统计和跟踪系统
6. 实现筛查效果评估指标计算

## Tasks / Subtasks

- [ ] 任务1：实现筛查结果判定系统 (AC: 1)

  - [ ] 创建src/modules/screening/result_processor.py文件
  - [ ] 实现ScreeningResultProcessor类，处理筛查结果
  - [ ] 添加结果判定逻辑（基于灵敏性和特异度）
  - [ ] 实现不确定结果的处理机制
  - [ ] 创建结果置信度评估功能
  - [ ] 添加结果质量控制和验证
- [ ] 任务2：实现假阳性和假阴性处理 (AC: 2)

  - [ ] 扩展结果处理器，添加假结果检测
  - [ ] 实现假阳性结果的后续处理流程
  - [ ] 添加假阴性结果的延迟发现建模
  - [ ] 创建假结果对个体心理影响建模
  - [ ] 实现假结果成本和负担计算
  - [ ] 添加假结果统计跟踪功能
- [ ] 任务3：实现后续诊断流程触发系统 (AC: 3)

  - [ ] 创建src/modules/screening/followup_manager.py文件
  - [ ] 实现FollowupManager类，管理后续诊断
  - [ ] 添加阳性结果的诊断性肠镜触发
  - [ ] 实现诊断等待时间建模  -暂不开发
  - [ ] 创建诊断容量和资源约束建模 -暂不开发
  - [ ] 添加诊断结果处理和反馈机制
- [ ] 任务4：实现筛查结果对个体状态影响 (AC: 4)

  - [ ] 扩展Individual类，添加筛查历史记录
  - [ ] 实现筛查结果对疾病检测的影响
  - [ ] 添加筛查结果对后续筛查行为的影响
  - [ ] 创建筛查结果对治疗时机的影响建模  -暂不开发
  - [ ] 实现筛查结果对生存预后的影响（筛查并被确诊为病灶的，则默认此处病灶被手术摘除）
  - [ ] 添加筛查结果心理和行为影响建模  -暂不开发
- [ ] 任务5：创建筛查结果统计跟踪系统 (AC: 5)

  - [ ] 创建src/services/screening_analytics.py文件
  - [ ] 实现ScreeningAnalytics类，统计筛查结果
  - [ ] 添加筛查性能指标计算（灵敏度、特异性、PPV、NPV）
  - [ ] 创建筛查结果趋势分析功能
  - [ ] 实现筛查质量监控和报警系统
  - [ ] 添加筛查结果可视化和报告功能
- [ ] 任务6：实现筛查效果评估指标计算 (AC: 6)

  - [ ] 创建src/modules/screening/effectiveness_metrics.py文件
  - [ ] 实现EffectivenessMetrics类，计算评估指标
  - [ ] 添加腺瘤检出率、临床前癌症检出率和癌症检出率的计算
  - [ ] 实现筛查相关癌症发病率降低和癌症死亡率降低的计算
  - [ ] 创建筛查生存获益评估功能（寿命年挽回和QALY提高）
  - [ ] 添加筛查成本效果比、成本效益比计算

## Dev Notes

### 筛查结果数据结构

```python
from dataclasses import dataclass
from typing import Optional, Dict, List
from enum import Enum
from datetime import datetime

class ScreeningResultType(Enum):
    NEGATIVE = "negative"
    POSITIVE = "positive"
    INDETERMINATE = "indeterminate"
    INADEQUATE = "inadequate"

class FollowupAction(Enum):
    NONE = "none"
    REPEAT_SCREENING = "repeat_screening"
    DIAGNOSTIC_COLONOSCOPY = "diagnostic_colonoscopy"
    SPECIALIST_REFERRAL = "specialist_referral"
    IMMEDIATE_TREATMENT = "immediate_treatment"

@dataclass
class ScreeningResult:
    individual_id: str
    tool_type: ScreeningToolType
    result_type: ScreeningResultType
    test_date: datetime
    confidence_score: float
    raw_value: Optional[float] = None
    is_true_positive: Optional[bool] = None
    is_true_negative: Optional[bool] = None
    followup_action: FollowupAction = FollowupAction.NONE
    processing_time: float = 0.0
    cost: float = 0.0
```

### 筛查结果判定逻辑

```python
class ScreeningResultProcessor:
    def __init__(self, tool_config: Dict):
        self.tool_config = tool_config
        self.sensitivity = tool_config['sensitivity_by_state']
        self.specificity = tool_config['specificity']
  
    def process_screening(
        self, 
        individual: Individual, 
        tool: ScreeningTool
    ) -> ScreeningResult:
        """处理筛查并生成结果"""
    
        # 获取个体真实疾病状态
        true_state = individual.current_disease_state
    
        # 计算检测概率
        detection_prob = self._calculate_detection_probability(individual, tool)
    
        # 生成筛查结果
        result_type = self._determine_result_type(true_state, detection_prob)
    
        # 创建结果对象
        result = ScreeningResult(
            individual_id=individual.id,
            tool_type=tool.tool_type,
            result_type=result_type,
            test_date=datetime.now(),
            confidence_score=self._calculate_confidence_score(detection_prob),
            is_true_positive=self._is_true_positive(true_state, result_type),
            is_true_negative=self._is_true_negative(true_state, result_type)
        )
    
        # 确定后续行动
        result.followup_action = self._determine_followup_action(result)
    
        return result
  
    def _determine_result_type(
        self, 
        true_state: DiseaseState, 
        detection_prob: float
    ) -> ScreeningResultType:
        """确定筛查结果类型"""
    
        if true_state == DiseaseState.NORMAL:
            # 正常状态：基于特异性判定
            if random.random() > self.specificity:
                return ScreeningResultType.POSITIVE  # 假阳性
            else:
                return ScreeningResultType.NEGATIVE  # 真阴性
        else:
            # 疾病状态：基于敏感性判定
            if random.random() < detection_prob:
                return ScreeningResultType.POSITIVE  # 真阳性
            else:
                return ScreeningResultType.NEGATIVE  # 假阴性
```

### 后续诊断流程管理

```python
class FollowupManager:
    def __init__(self, healthcare_system_config: Dict):
        self.config = healthcare_system_config
        self.diagnostic_capacity = healthcare_system_config['diagnostic_capacity']
        self.waiting_times = healthcare_system_config['waiting_times']
  
    def schedule_followup(
        self, 
        screening_result: ScreeningResult, 
        individual: Individual
    ) -> Optional[DiagnosticAppointment]:
        """安排后续诊断"""
    
        if screening_result.followup_action == FollowupAction.DIAGNOSTIC_COLONOSCOPY:
            # 检查诊断容量
            if self._check_diagnostic_capacity():
                waiting_time = self._calculate_waiting_time()
            
                appointment = DiagnosticAppointment(
                    individual_id=individual.id,
                    procedure_type=DiagnosticProcedure.COLONOSCOPY,
                    scheduled_date=screening_result.test_date + timedelta(days=waiting_time),
                    referring_screening=screening_result
                )
            
                return appointment
            else:
                # 容量不足，延长等待时间
                self._handle_capacity_constraint(screening_result, individual)
    
        return None
  
    def process_diagnostic_result(
        self, 
        diagnostic_result: DiagnosticResult, 
        individual: Individual
    ):
        """处理诊断结果"""
    
        if diagnostic_result.findings:
            # 发现病变，更新个体状态
            self._update_individual_state(individual, diagnostic_result)
        
            # 触发治疗流程
            self._initiate_treatment(individual, diagnostic_result)
    
        # 记录诊断历史
        individual.add_diagnostic_history(diagnostic_result)
```

### 筛查效果评估指标

```python
class EffectivenessMetrics:
    def __init__(self, population: Population):
        self.population = population
        self.screening_results = []
        self.diagnostic_results = []
  
    def calculate_detection_metrics(self) -> Dict:
        """计算检出相关指标"""
        total_screenings = len(self.screening_results)
        positive_results = [r for r in self.screening_results if r.result_type == ScreeningResultType.POSITIVE]
    
        # 基本检出指标
        positivity_rate = len(positive_results) / total_screenings if total_screenings > 0 else 0
    
        # 癌症检出率
        cancer_detected = self._count_cancer_detected()
        cancer_detection_rate = cancer_detected / total_screenings * 1000  # 每1000次筛查
    
        # 早期癌症检出率
        early_cancer_detected = self._count_early_cancer_detected()
        early_detection_rate = early_cancer_detected / cancer_detected if cancer_detected > 0 else 0
    
        return {
            'positivity_rate': positivity_rate,
            'cancer_detection_rate': cancer_detection_rate,
            'early_detection_rate': early_detection_rate,
            'total_screenings': total_screenings,
            'positive_results': len(positive_results)
        }
  
    def calculate_performance_metrics(self) -> Dict:
        """计算筛查性能指标"""
        tp = self._count_true_positives()
        tn = self._count_true_negatives()
        fp = self._count_false_positives()
        fn = self._count_false_negatives()
    
        sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0
        specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
        ppv = tp / (tp + fp) if (tp + fp) > 0 else 0  # 阳性预测值
        npv = tn / (tn + fn) if (tn + fn) > 0 else 0  # 阴性预测值
    
        return {
            'sensitivity': sensitivity,
            'specificity': specificity,
            'positive_predictive_value': ppv,
            'negative_predictive_value': npv,
            'true_positives': tp,
            'true_negatives': tn,
            'false_positives': fp,
            'false_negatives': fn
        }
```

### 筛查结果统计分析

```python
class ScreeningAnalytics:
    def generate_screening_report(
        self, 
        screening_results: List[ScreeningResult]
    ) -> Dict:
        """生成筛查结果分析报告"""
    
        report = {
            'summary_statistics': self._calculate_summary_stats(screening_results),
            'performance_metrics': self._calculate_performance_metrics(screening_results),
            'temporal_trends': self._analyze_temporal_trends(screening_results),
            'tool_comparison': self._compare_screening_tools(screening_results),
            'quality_indicators': self._assess_quality_indicators(screening_results)
        }
    
        return report
  
    def monitor_screening_quality(
        self, 
        screening_results: List[ScreeningResult]
    ) -> List[QualityAlert]:
        """监控筛查质量并生成警报"""
        alerts = []
    
        # 检查异常高的阳性率
        positivity_rate = self._calculate_positivity_rate(screening_results)
        if positivity_rate > 0.15:  # 阳性率超过15%
            alerts.append(QualityAlert(
                type="HIGH_POSITIVITY_RATE",
                message=f"阳性率异常高: {positivity_rate:.2%}",
                severity="WARNING"
            ))
    
        # 检查假阳性率
        false_positive_rate = self._calculate_false_positive_rate(screening_results)
        if false_positive_rate > 0.10:  # 假阳性率超过10%
            alerts.append(QualityAlert(
                type="HIGH_FALSE_POSITIVE_RATE",
                message=f"假阳性率过高: {false_positive_rate:.2%}",
                severity="ERROR"
            ))
    
        return alerts
```

### Testing

#### 测试文件位置

- `tests/unit/test_result_processor.py`
- `tests/unit/test_followup_manager.py`
- `tests/unit/test_effectiveness_metrics.py`
- `tests/integration/test_screening_workflow.py`

#### 测试标准

- 筛查结果判定逻辑准确性测试
- 假阳性/假阴性处理测试
- 后续诊断流程触发测试
- 效果评估指标计算测试
- 统计分析功能测试

#### 测试框架和模式

- 使用pytest参数化测试不同筛查场景
- Mock个体疾病状态测试结果判定
- 统计检验验证性能指标计算
- 集成测试验证完整筛查流程

#### 特定测试要求

- 结果判定准确性: 与理论值偏差 < 1%
- 性能指标计算精度: 误差 < 0.1%
- 后续流程触发一致性: 100%正确触发
- 统计分析准确性: 与标准算法结果一致

## Change Log

| Date       | Version | Description  | Author       |
| ---------- | ------- | ------------ | ------------ |
| 2025-07-31 | 1.0     | 初始故事创建 | Scrum Master |

## Dev Agent Record

*此部分将由开发代理在实施过程中填写*

### Agent Model Used

*待填写*

### Debug Log References

*待填写*

### Completion Notes List

*待填写*

### File List

*待填写*

## QA Results

*此部分将由QA代理在完成故事实施的QA审查后填写*
